import { getSpacesByCommunityId } from '@modules/partnersApi/services/partnersSpace'
import * as spacesRepo from '@modules/communities/repositories/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import { ExternalLinkService } from '@modules/communities/types/externalLink'

jest.mock('@modules/communities/repositories/space')

describe('partnersSpace', () => {
  describe('getSpacesByCommunityId', () => {
    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('should return layoutType from floorPlan.name when available', async () => {
      const mockSpaces = [
        {
          _id: 'space-id-1',
          token: 'test-token',
          type: SpaceType.Unit,
          bedrooms: 2,
          bathrooms: 1,
          unit: '101',
          building: {
            _id: 'building-id',
            address: {
              street: '123 Test St',
              city: 'Test City',
              state: 'TS',
              postalCode: '12345'
            }
          },
          isModelUnit: false,
          floorPlan: {
            name: 'Studio Deluxe',
            externalId: 'studio-deluxe-001'
          },
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [
            {
              externalId: 'ext-id-1',
              service: ExternalLinkService.Entrata,
              externalName: 'test-name'
            }
          ]
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(1)
      expect(result[0].unitDetails?.layoutType).toBe('Studio Deluxe')
    })

    it('should return null layoutType when floorPlan.name is not available', async () => {
      const mockSpaces = [
        {
          _id: 'space-id-1',
          token: 'test-token',
          type: SpaceType.Unit,
          bedrooms: 2,
          bathrooms: 1,
          unit: '101',
          building: {
            _id: 'building-id',
            address: {
              street: '123 Test St',
              city: 'Test City',
              state: 'TS',
              postalCode: '12345'
            }
          },
          isModelUnit: false,
          // No floorPlan field
          community: {
            _id: 'community-id',
            organization: {
              _id: 'org-id'
            }
          },
          isComplete: true,
          isVisible: true,
          externalLinks: [
            {
              externalId: 'ext-id-1',
              service: ExternalLinkService.Entrata,
              externalName: 'test-name'
            }
          ]
        }
      ]

      jest
        .spyOn(spacesRepo, 'findSpacesWithExternalLinksByCommunityId')
        .mockResolvedValue(mockSpaces as any)

      const result = await getSpacesByCommunityId({
        id: 'community-id',
        organizationId: 'org-id'
      })

      expect(result).toHaveLength(1)
      expect(result[0].unitDetails?.layoutType).toBe(null)
    })
  })
})
